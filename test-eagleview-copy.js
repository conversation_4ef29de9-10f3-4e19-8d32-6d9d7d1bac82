#!/usr/bin/env node

/**
 * Test script for EagleView copy functionality
 * Tests the new POST /eagleview/copy endpoint
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust if your server runs on a different port
const TEST_ACCESS_CODE = 'test-copy-user-123';
const TEST_PURCHASE_ID = 1; // You'll need to use an actual purchase ID from your database

async function testEagleViewCopy() {
    console.log('🧪 Testing EagleView Copy Functionality\n');

    try {
        // Test 1: Missing parameters
        console.log('1. Testing missing parameters...');
        try {
            const response = await axios.post(`${BASE_URL}/eagleview/copy`, {});
            console.log('❌ Should have failed with missing parameters');
        } catch (error) {
            if (error.response && error.response.status === 400) {
                console.log('✅ Correctly rejected missing parameters');
                console.log(`   Response: ${error.response.data.message}\n`);
            } else {
                console.log('❌ Unexpected error:', error.message);
            }
        }

        // Test 2: Invalid purchase ID
        console.log('2. Testing invalid purchase ID...');
        try {
            const response = await axios.post(`${BASE_URL}/eagleview/copy`, {
                purchaseId: 999999,
                accessCode: TEST_ACCESS_CODE
            });
            console.log('❌ Should have failed with invalid purchase ID');
        } catch (error) {
            if (error.response && error.response.status === 404) {
                console.log('✅ Correctly rejected invalid purchase ID');
                console.log(`   Response: ${error.response.data.message}\n`);
            } else {
                console.log('❌ Unexpected error:', error.message);
            }
        }

        // Test 3: Valid copy request (if you have a valid purchase ID)
        console.log('3. Testing valid copy request...');
        console.log(`   Using purchaseId: ${TEST_PURCHASE_ID}, accessCode: ${TEST_ACCESS_CODE}`);
        
        try {
            const response = await axios.post(`${BASE_URL}/eagleview/copy`, {
                purchaseId: TEST_PURCHASE_ID,
                accessCode: TEST_ACCESS_CODE
            });

            if (response.status === 201) {
                console.log('✅ Copy request successful!');
                console.log(`   New Purchase ID: ${response.data.purchaseId}`);
                console.log(`   Cost: $${response.data.cost}`);
                console.log(`   Token generated: ${response.data.token ? 'Yes' : 'No'}`);
                console.log(`   Original Purchase ID: ${response.data.originalPurchaseId}`);
                console.log(`   Zoom levels: ${response.data.minZoom}-${response.data.maxZoom}\n`);
            } else {
                console.log('❌ Unexpected response status:', response.status);
            }
        } catch (error) {
            if (error.response) {
                console.log('❌ Copy request failed:');
                console.log(`   Status: ${error.response.status}`);
                console.log(`   Error: ${error.response.data.error}`);
                console.log(`   Message: ${error.response.data.message}`);
                
                if (error.response.status === 402) {
                    console.log(`   Required quota: $${error.response.data.required}`);
                    console.log(`   Available quota: $${error.response.data.available}`);
                }
            } else {
                console.log('❌ Network error:', error.message);
            }
        }

        console.log('\n🎯 Test Summary:');
        console.log('- Parameter validation: Working');
        console.log('- Invalid ID handling: Working');
        console.log('- Copy functionality: Check above results');
        console.log('\n📝 Note: For a complete test, ensure you have:');
        console.log('1. A valid purchase ID in your database');
        console.log('2. Sufficient quota for the test access code');
        console.log('3. The server running on the correct port');

    } catch (error) {
        console.error('❌ Test setup error:', error.message);
    }
}

// Run the test
if (require.main === module) {
    testEagleViewCopy();
}

module.exports = { testEagleViewCopy };
