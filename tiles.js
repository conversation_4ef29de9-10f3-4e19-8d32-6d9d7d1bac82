const db = require('./db')
const geofunctions = require('./geofunctions')
const turf = require('@turf/turf')
const nearmap = require('./nearmap')
const storage = require('./storage')
const uuidv4 = require('uuid/v4')
const fs = require('fs')
const nearmapBypass = require('./nearmap-downloader.js')
const eagleview = require('./eagleview.js')


const uploadExternal = async (buffer, accessCode, bounds, date, type) => {
    try {
        const uid = uuidv4()
        const newfile = uid + type;
        const cost = 0;
        const buildingID = 0;
        const mapID = 0;
        const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds)
        const boundsPoly = turf.bboxPolygon(turfBounds)
        const url = await storage.uploadFileBuffer(buffer, newfile, type)
        const insertedRecord = await db.addTile(accessCode, url, date, turfBounds, boundsPoly.geometry, 'nearmap', cost)
        await db.logAccess(insertedRecord.id, false, false, buildingID, accessCode, cost, mapID)
        return url;
    } catch (ex) {
        console.log(ex)
        return false;
    }
}
const getCached = async (accessCode, bounds, worker) => {
    return new Promise(async function (resolve, reject) {
        const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds)
        const boundsPoly = turf.bboxPolygon(turfBounds)
        const p1= db.allCached(boundsPoly)

        // Check if this is a worker request
        const isWorker = typeof worker == 'undefined' ? false : worker == 0 ? false : true;

        Promise.all([p1]).then(values => {
            var returnValue = [];
            for(var i=0;i<values[0].length;i++)
            {
                //need to fix, it was not working after copymaps, so i need to work on it and fix logic for this.
               var selfCached = isSelfCached2(values[0][i], accessCode)

               // For EagleView: workers get access to all cached purchases due to unlimited agreement
               var isEagleViewWorkerAccess = isWorker && values[0][i].provider === 'eagleview';

                if(selfCached || isEagleViewWorkerAccess)
                    returnValue.push(values[0][i]);
            }
            resolve(returnValue);
        })
    })
}

const getURLCached = (url) => {
    return new Promise(function (resolve, reject) {
        
        const p1= db.urlCached(url)
        Promise.all([p1]).then(values => {
            var returnValue = [];
            for(var i=0;i<values[0].length;i++)
            {
                returnValue.push(values[0][i]);
            }
            resolve(returnValue);
        })
    })
}

const searchProviders = async (bounds, accesscode, worker) => {
    // Convert bounds to turf bounds and get the bounding polygon.
    const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds);
    const boundsPoly = turf.bboxPolygon(turfBounds);

    // Initialize variables for the search results.
    let eaglesearch;

    
    // Check for byPass flag.
    let byPassFlag = await db.checkBypass(accesscode);
    let byPass = (worker == 1) || byPassFlag;
    
    

   try {
       eaglesearch = await eagleview.getCapabilities(boundsPoly);
   } catch (error) {
       console.error("Error in fetching eagleview status: ", error);
       eaglesearch = [];
   }

    

    
    if(!eaglesearch  || eaglesearch.length == 0) {
        throw new Error("Search Failed");
    }

    // Apply byPass condition.
  
    
    // Return the results.
   
    return {
        picsearch: [],
        nearsearch: [],
        eaglesearch: eaglesearch,
        bounds: turfBounds
    };
}






const parseSearchCacheNear = (nearsearch) => {
    return new Promise(function (resolve, reject) {
        var promisearray = [];
        if(typeof nearsearch.im == 'undefined')
        {
            resolve(nearsearch);
            return;
        }
        if(nearsearch.im.length == 0)
        {
            resolve(nearsearch);
            return;
        }
        for(var i=0;i<nearsearch.im.length;i++)
        {
            var pr = db.checkCachePic(nearsearch.im[i].bboxpoly, nearsearch.im[i].date) 
            promisearray.push(pr);    
        }
        Promise.all(promisearray).then(values => {
            console.log(values.length)
            

            for(var i=0;i<values.length;i++)
            {
                
                if(typeof values[i][0] != 'undefined')
                {
                    nearsearch.im[i].tile = values[i][0].tiles_id
                }
                else
                {
                    nearsearch.im[i].tile = null
                }
                if (i == (values.length - 1)) {
                    resolve(nearsearch);
                }
            }
        })
    })
}






const isSelfCached2 = (data, accessCode) => {
    var array = data.tiles_accesscode.split(",");
    console.log(accessCode)
    if (array.indexOf(accessCode) != -1)
        return true
    else
        return false
}


const getCachedTileByID = (accessCode, tileID, worker, bid, mid) => {
    return new Promise(function (resolve, reject) {
        const p1 = db.userStatus(accessCode)
        const p2 = db.getTile(tileID)
        const p3 = db.getQuota(accessCode)
        Promise.all([p1, p2, p3]).then(values => {
            const isCached = values[1].length != 0
            const isForced = values[0].length > 0 ? values[0].users_forcedownload == 1 : false
            const remainQuota = values[0].length > 0 ? values[2][0][0].allocation - values[2][1][0].usage : 0
            const isWorker = typeof worker == 'undefined' ? false : worker == 0 ? false : true
            const selfCached = values[1].length == 0 ? false : isSelfCached2(values[1][0], accessCode)
            const buildingID = typeof bid == 'undefined' ? 0 : bid
            const mapID = typeof mid == 'undefined' ? 0 : mid
            if(selfCached)
            {
                return resolve({
                    error: false,
                    url: values[1][0].tiles_url,
                    quota: remainQuota,
                    provider: values[1][0].tiles_provider,
                    date: values[1][0].tiles_imagery_date,
                    bounds: values[1][0].t_bounds
                })
            }
            else if(isWorker && isCached)
            {
                var array = values[1][0].tiles_accesscode.split(",");
                array.push(accessCode)
                db.addTileAccess(tileID, array.join()).then(values2 => {
                    return resolve({
                        error: false,
                        url: values[1][0].tiles_url,
                        quota: remainQuota,
                        provider: values[1][0].tiles_provider,
                        date: values[1][0].tiles_imagery_date,
                        bounds: values[1][0].t_bounds
                    })
                })
            }
            else if(isCached && (remainQuota-values[1][0].tiles_cost>=0))
            {
                var array = values[1][0].tiles_accesscode.split(",");
                array.push(accessCode)
                db.addTileAccess(tileID, array.join()).then(values2 => {
                     resolve({
                        error: false,
                        url: values[1][0].tiles_url,
                        quota: remainQuota-values[1][0].tiles_cost,
                        provider: values[1][0].tiles_provider,
                        date: values[1][0].tiles_imagery_date,
                        bounds: values[1][0].t_bounds
                    })
                    db.logAccess(tileID, true, false, buildingID, accessCode, values[1][0].tiles_cost, mapID)
                    return;
                }) 
            }
            else
            {
                return resolve({
                    error: true,
                    url: null,
                    quota: null,
                    provider: null,
                    date: null,
                    bounds:null
                })
            }
        })
    })
}

const checkNearMapCache = async (bounds, date) => {
    const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds)
    const boundsPoly = turf.bboxPolygon(turfBounds)
    
    const p1= await db.allCached(boundsPoly)
    let filteredArray = p1.filter(item => {
        let imgDate = new Date(item.date);
        let formattedDate = imgDate.getFullYear() + "-" + (imgDate.getMonth()+1).toString().padStart(2, '0') + "-" + imgDate.getDate().toString().padStart(2, '0');
        return formattedDate === date && item.coverage > 1;
      });
    return filteredArray
}
const downloadNearMapTile = async (bounds, date, accessCode, worker, bid, cost, mid) => {

    const values = await Promise.all([db.userStatus(accessCode), db.getQuota(accessCode), checkNearMapCache(bounds, date)]);
    const turfBounds = geofunctions.convertBoundsStringToTurfBounds(bounds);
    const boundsPoly = turf.bboxPolygon(turfBounds);
    const remainQuota = values[0].length > 0 ? values[1][0][0].allocation - values[1][1][0].usage : 0;
    const isWorker = typeof worker == 'undefined' ? false : worker == 0 ? false : true;
    const buildingID = typeof bid == 'undefined' ? 0 : bid;
    const mapID = typeof mid == 'undefined' ? 0 : mid;
    const cached = values[2].length>0 ? true : false;
    let byPassFlag = await db.checkBypass(accessCode);
    let byPass = isWorker || byPassFlag;
    byPass = false;
    if(cached) {
     
        if(isWorker || (remainQuota-cost >= 0)) {
            const polygon = JSON.parse(values[2][0].bounds);
            const result = {
                error: false,
                url: values[2][0].tiles_url,
                quota: remainQuota-cost,
                provider: "Nearmap",
                bounds: polygon,
                date: date
            };
            if(!isWorker) {
                db.logAccess(values[2][0].id, true, false, buildingID, accessCode, cost, mapID)
                .catch((e) => console.log(e));
                let arr = values[2][0].tiles_accesscode.split(",");
                arr.push(accessCode)
                db.addTileAccess(values[2][0].id, arr.join())
            }
            return result;
        }
    }
    if(isWorker || (remainQuota-cost >= 0)) {
       
        let data = { bounds: turfBounds, date: date, accessCode: accessCode, worker: isWorker, bid: buildingID, cost: cost, mid: mapID };
        //const files = await nearmap.downloadTile(data);
        const files = byPass ? await nearmapBypass.downloadTile(turfBounds) : await nearmap.downloadTile(data);
        
        const values2 = await Promise.all([storage.uploadFile(files.ofile), storage.uploadFile(files.cfile)]);
        
        const result = {
            error: false,
            url: values2[1],
            quota: remainQuota-cost,
            provider: "Nearmap",
            bounds: boundsPoly.geometry,
            date: date
        };
        const result3 = await db.addTile(accessCode, values2[1], date, turfBounds, boundsPoly.geometry, 'nearmap', cost);
        if(!isWorker) {
            db.logAccess(result3.id, false, false, buildingID, accessCode, cost, mapID)
            .catch((e) => console.log(e));
        }
        fs.unlinkSync(files.ofile);
        fs.unlinkSync(files.cfile);
        return result;
    } else {
        return {
            error: true,
            url: null,
            quota: remainQuota,
            provider: null,
            bounds: null,
            date: null
        };
    }
};




module.exports = {
    getCached,
    downloadNearMapTile,
    searchProviders,
    getCachedTileByID,
    getURLCached,
    uploadExternal
}
