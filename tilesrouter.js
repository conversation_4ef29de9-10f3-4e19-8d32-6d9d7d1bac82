const express = require('express')
const router = express.Router()
const db = require('./db')
const tiles = require('./tiles')
const storage = require('./storage')
const multer = require('multer');
const geofunctions = require('./geofunctions');
const turf = require('@turf/turf');
const booleanIntersects = require('@turf/boolean-intersects').default;
const upload = multer();

const eagleview = require('./eagleview');
const jwt = require('jsonwebtoken');



router.get('/', function (req, res) {
    res.send('Not applicable')
})

router.post('/upload-external-tile', upload.single('file'), async function (req, res) {
    try {
        const date = req.query.date;
        const bounds = req.query.bounds;
        const accessCode = req.query.accessCode;
        const type = req.query.type || '.png'
        const url = await tiles.uploadExternal(req.file.buffer, accessCode,bounds,date,type);
        if(url)
            res.json({url: url})
        else
            res.sendStatus(500);

    } catch (ex)
    {
        console.log(ex)
        res.sendStatus(500)
    }
    

})

router.get('/searchcached', function (req, res) {
    tiles.getCached(req.query.accesscode, req.query.bounds, req.query.worker).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/searchcachedbyurl', function (req, res) {
    tiles.getURLCached(req.query.url).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/searchcachedbyurlmapbuilder', function (req, res) {
    tiles.getURLCached(req.query.url).then(function (result) {
        db.copyTile(req.query.accesscode, req.query.url)
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/search', function (req, res) {
  
    tiles.searchProviders(req.query.bounds, req.query.accesscode, req.query.worker).then(function (result) {
        res.send(result)
    }).catch(function (e) {
		console.log(e)
        res.send(e)
    })
})



router.get('/downloadnearmaptile', function (req, res) { 
   
    tiles.downloadNearMapTile(req.query.bounds, req.query.date, req.query.accesscode, req.query.worker, req.query.bid, req.query.cost, req.query.mid).then(function (result) {
        res.send(result)
    }).catch(function (e) {
		
        res.send(e)
    })
})



router.get('/syncstorage', function (req, res) {
    
    storage.syncStorage().then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/getcachedtile', function(req, res) {
    tiles.getCachedTileByID(req.query.accesscode, req.query.tileid, req.query.worker, req.query.bid, req.query.mid).then(function (result) {
        res.send(result)
    }).catch(function (e){
        res.send(e)
    })
})










router.post('/eagleview/purchase', async (req, res) => {
    try {
        const { accesscode, layer, bounds, tileMatrixSet, imageryDate, minZoom, maxZoom, worker } = req.body;
        console.log('EagleView Purchase Request:', req.body);

        if (!accesscode || !layer || !bounds || !tileMatrixSet) {
            return res.status(400).send('Missing required parameters');
        }

        // CRITICAL FIX: Convert bounds array to proper GeoJSON Polygon
        // Frontend sends bounds as array of coordinates, but cost manager expects GeoJSON
        const boundsGeoJSON = {
            type: 'Polygon',
            coordinates: [bounds]
        };

        // Determine if this is a worker request
        const isWorker = typeof worker == 'undefined' ? false : worker == 0 ? false : true;

        // CRITICAL FIX: Use unified cost management to enforce quota limits
        const purchaseOptions = {
            layerId: layer,
            tileMatrixSet: tileMatrixSet,
            imageryDate: imageryDate, // Pass the imagery date from the frontend
            minZoom: minZoom || 7, // Default to 7 if not provided
            maxZoom: maxZoom || 22, // Default to 22 if not provided
            isWorker: isWorker // Support worker requests for free EagleView access
        };

        const purchaseResult = await db.checkQuotaAndPurchase(
            accesscode,
            boundsGeoJSON,
            'eagleview',
            purchaseOptions
        );

        if (!purchaseResult.success) {
            console.log(`EagleView purchase denied for ${accesscode}: ${purchaseResult.error}`);

            if (purchaseResult.error === 'insufficient_quota') {
                return res.status(402).json({
                    error: 'insufficient_quota',
                    message: 'Insufficient quota for this purchase',
                    required: purchaseResult.required,
                    available: purchaseResult.available
                });
            }

            return res.status(500).json({
                error: 'purchase_failed',
                message: 'Unable to process purchase'
            });
        }

        console.log(`EagleView purchase approved for ${accesscode}: ID ${purchaseResult.purchaseId}, Cost: $${purchaseResult.cost}`);

        // SIMPLIFIED: Generate JWT token for this specific purchase only (not all layer purchases)
        const token = jwt.sign(
            {
                accesscode,
                layer,
                purchaseId: purchaseResult.purchaseId, // Include purchase ID for validation
                bounds: [boundsGeoJSON.coordinates[0]], // Only the bounds of this specific purchase
                tileMatrixSet,
                minZoom: purchaseOptions.minZoom,
                maxZoom: purchaseOptions.maxZoom,
                exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // Expires in 24 hours
            },
            process.env.JWT_SECRET || '54a6f406-5cf2-46b7-843c-079f6084f8ba'
        );

        res.status(201).json({
            id: purchaseResult.purchaseId, // Make ID prominent for frontend
            purchaseId: purchaseResult.purchaseId, // Keep for backward compatibility
            token: token,
            bounds: boundsGeoJSON.coordinates[0], // Only this purchase's bounds
            cost: purchaseResult.cost,
            reason: purchaseResult.reason,
            remainingQuota: purchaseResult.remainingQuota,
            minZoom: purchaseOptions.minZoom,
            maxZoom: purchaseOptions.maxZoom
        });

    } catch (error) {
        console.error('EagleView purchase error:', error);
        res.status(500).json({
            error: 'server_error',
            message: 'Error processing purchase'
        });
    }
});
router.get('/eagleview/capabilities', async (req, res) => {
    try {
        const capabilities = await eagleview.getCapabilities(req.query.bounds);
        res.json(capabilities);
    } catch (error) {
        console.error(error);
        res.status(500).send('Error fetching EagleView capabilities');
    }
});

// Generate JWT token for existing EagleView purchase (for cached results) - ID-based
router.post('/eagleview/token', async (req, res) => {
    try {
        const { accesscode, purchaseId, layer, tileMatrixSet } = req.body;

        // SIMPLIFIED: Support both ID-based (new) and layer-based (legacy) requests
        if (!accesscode || (!purchaseId && !layer)) {
            return res.status(400).json({
                error: 'missing_parameters',
                message: 'accesscode and either purchaseId or layer are required'
            });
        }

        let purchaseData;

        if (purchaseId) {
            // NEW: ID-based approach - get specific purchase by ID
            purchaseData = await db.getEagleViewPurchaseById(accesscode, purchaseId);
        } else {
            // LEGACY: Layer-based approach - get most recent purchase for layer
            const { bounds: allPurchasedBounds, tileMatrixSet: layerTileMatrixSet } = await db.getEagleViewPurchases(accesscode, layer);
            if (!allPurchasedBounds || allPurchasedBounds.length === 0) {
                return res.status(403).json({
                    error: 'not_purchased',
                    message: 'No valid purchase found for this layer'
                });
            }
            // For legacy support, use the first (most recent) purchase bounds
            purchaseData = {
                id: null, // Legacy mode doesn't have specific purchase ID
                layer_id: layer,
                bounds: allPurchasedBounds[0],
                tile_matrix_set: layerTileMatrixSet || tileMatrixSet,
                min_zoom: 7, // Default values for legacy
                max_zoom: 22
            };
        }

        if (!purchaseData) {
            return res.status(403).json({
                error: 'not_purchased',
                message: 'No valid purchase found'
            });
        }

        // Generate JWT token for the specific purchase
        const token = jwt.sign(
            {
                accesscode,
                layer: purchaseData.layer_id,
                purchaseId: purchaseData.id, // Include purchase ID for validation
                bounds: [purchaseData.bounds], // Only this purchase's bounds
                tileMatrixSet: purchaseData.tile_matrix_set || tileMatrixSet || 'GoogleMapsCompatible_7-22',
                minZoom: purchaseData.min_zoom || 7,
                maxZoom: purchaseData.max_zoom || 22,
                exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // Expires in 24 hours
            },
            process.env.JWT_SECRET || '54a6f406-5cf2-46b7-843c-079f6084f8ba'
        );

        res.status(200).json({
            token: token,
            id: purchaseData.id,
            layer: purchaseData.layer_id,
            bounds: purchaseData.bounds, // Only this purchase's bounds
            minZoom: purchaseData.min_zoom || 7,
            maxZoom: purchaseData.max_zoom || 22
        });

    } catch (error) {
        console.error('EagleView token generation error:', error);
        res.status(500).json({
            error: 'server_error',
            message: 'Error generating token'
        });
    }
});

// Copy EagleView purchase to a new row with different accessCode
router.post('/eagleview/copy', async (req, res) => {
    try {
        const { purchaseId, accessCode } = req.body;
        console.log('EagleView Copy Request:', req.body);

        if (!purchaseId || !accessCode) {
            return res.status(400).json({
                error: 'missing_parameters',
                message: 'purchaseId and accessCode are required'
            });
        }

        // Get the original purchase data
        const originalPurchase = await db.getEagleViewPurchaseByIdAny(purchaseId);
        if (!originalPurchase) {
            return res.status(404).json({
                error: 'purchase_not_found',
                message: 'Original purchase not found'
            });
        }

        // Create the bounds GeoJSON from the original purchase
        const boundsGeoJSON = {
            type: 'Polygon',
            coordinates: [originalPurchase.bounds]
        };

        // Copy the purchase with the new accessCode using the cost manager
        const purchaseOptions = {
            layerId: originalPurchase.layer_id,
            tileMatrixSet: originalPurchase.tile_matrix_set,
            minZoom: originalPurchase.min_zoom || 7,
            maxZoom: originalPurchase.max_zoom || 22,
            isWorker: false // Default to non-worker for copied purchases
        };

        const copyResult = await db.checkQuotaAndPurchase(
            accessCode,
            boundsGeoJSON,
            'eagleview',
            purchaseOptions
        );

        if (!copyResult.success) {
            console.log(`EagleView copy denied for ${accessCode}: ${copyResult.error}`);

            if (copyResult.error === 'insufficient_quota') {
                return res.status(402).json({
                    error: 'insufficient_quota',
                    message: 'Insufficient quota for this copy operation',
                    required: copyResult.required,
                    available: copyResult.available
                });
            }

            return res.status(500).json({
                error: 'copy_failed',
                message: 'Unable to process copy operation'
            });
        }

        console.log(`EagleView copy successful for ${accessCode}: New ID ${copyResult.purchaseId}, Cost: $${copyResult.cost}`);

        // Generate JWT token for the new purchase
        const token = jwt.sign(
            {
                accesscode: accessCode,
                layer: originalPurchase.layer_id,
                purchaseId: copyResult.purchaseId,
                bounds: [boundsGeoJSON.coordinates[0]],
                tileMatrixSet: originalPurchase.tile_matrix_set,
                minZoom: purchaseOptions.minZoom,
                maxZoom: purchaseOptions.maxZoom,
                exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24) // Expires in 24 hours
            },
            process.env.JWT_SECRET || '54a6f406-5cf2-46b7-843c-079f6084f8ba'
        );

        res.status(201).json({
            id: copyResult.purchaseId,
            purchaseId: copyResult.purchaseId,
            token: token,
            bounds: boundsGeoJSON.coordinates[0],
            cost: copyResult.cost,
            reason: copyResult.reason,
            remainingQuota: copyResult.remainingQuota,
            minZoom: purchaseOptions.minZoom,
            maxZoom: purchaseOptions.maxZoom,
            originalPurchaseId: purchaseId
        });

    } catch (error) {
        console.error('EagleView copy error:', error);
        res.status(500).json({
            error: 'server_error',
            message: 'Error processing copy operation'
        });
    }
});


module.exports = router