const pgp = require('pg-promise')({

});

const turf = require('@turf/turf')
const CostManager = require('./cost-manager')
const cn = {
  user: 'pictometry',
  host: '127.0.0.1',
  database: 'pictometry_v3',  // NEW DATABASE for /api3
  password: 'Y^<Ta5C+H(d|$,Gj',
  port: 5432
}

const pdb = pgp(cn)

// Initialize Cost Manager with database connection
const costManager = new CostManager(pdb)

const isCustomer = (accessCode) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT * FROM users WHERE users_accesscode = $1::text limit 1", [accessCode])
      .then(function (data) {
        resolve(data)
      })
      .catch(function (error) {
        reject(error)
      });
  })

}

const copyTile = async (accesscode,url) => {
  return new Promise(async function (resolve, reject) {
    pdb.any("select id,accesscode from imagery_purchases where storage_url = $1", [url])
      .then(async function (tiles) {
        for(let tile of tiles)
        {
            let array = tile.accesscode.split(",");
            if (array.indexOf(accesscode) === -1) {
              array.push(accesscode)
              await addTileAccess(tile.id, array.join())
            }

        }
        resolve()
      })
      .catch(function (error) {
        reject(error)
      });
  })
}

const userStatus = (accessCode) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT * FROM users WHERE users_accesscode = $1::text limit 1", [accessCode])
      .then(function (data) {
        resolve(data)
      })
      .catch(function (error) {
        reject(error)
      });
  })

}

const checkCachePic = (bounds, dt) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT id as tiles_id, accesscode as tiles_accesscode, imagery_date AS date, provider AS tiles_provider FROM imagery_purchases WHERE ST_Equals(bounds, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326)) AND imagery_date=$2 AND provider='pictometry'", [bounds,dt])
    .then(function (data) {
      resolve(data)
    })
    .catch(function (error) {
      reject(error)
    });
  })
}

const checkCacheNear = (bounds, dt) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT id as tiles_id, accesscode as tiles_accesscode, imagery_date AS date, provider AS tiles_provider FROM imagery_purchases WHERE ST_Within(bounds, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326)) AND imagery_date=$2 AND provider='nearmap'", [bounds,dt])
    .then(function (data) {
      resolve(data)
    })
    .catch(function (error) {
      reject(error)
    });
  })
}

const allCached = (bounds) => {
  return new Promise(function (resolve, reject) {
    // Return both cached tiles (with storage_url) and purchased EagleView layers (without storage_url)
    // For EagleView: layer_id serves as the identifier for on-demand WMTS access
    // Deduplicate EagleView layers by showing only the most recent purchase per layer_id
    pdb.any(`
      WITH deduplicated_eagleview AS (
        SELECT DISTINCT ON (layer_id)
          id, bounds, accesscode, layer_id, imagery_date, provider, tile_matrix_set, purchase_date, min_zoom, max_zoom
        FROM imagery_purchases
        WHERE provider = 'eagleview'
        AND layer_id IS NOT NULL
        AND st_intersects(bounds, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326))
        ORDER BY layer_id, purchase_date DESC
      ),
      cached_tiles AS (
        SELECT
          id, bounds, accesscode, storage_url, imagery_date, provider,
          NULL as layer_id, NULL as tile_matrix_set, purchase_date, 7 as min_zoom, 22 as max_zoom
        FROM imagery_purchases
        WHERE provider != 'eagleview'
        AND storage_url IS NOT NULL
        AND storage_url != ''
        AND st_intersects(bounds, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326))
      )
      SELECT
        id,
        ST_AsGeoJSON(bounds) as bounds,
        (st_area(st_intersection(bounds, ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326)))/st_area(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326))) as coverage,
        accesscode as tiles_accesscode,
        CASE
          WHEN provider = 'eagleview' THEN layer_id
          ELSE storage_url
        END as tiles_url,
        imagery_date AS date,
        provider AS tiles_provider,
        layer_id,
        tile_matrix_set,
        min_zoom,
        max_zoom,
        -- SIMPLIFIED: Include purchase ID for ID-based communication
        CASE
          WHEN provider = 'eagleview' THEN id
          ELSE NULL
        END as purchase_id
      FROM (
        SELECT id, bounds, accesscode, layer_id as storage_url, imagery_date, provider, layer_id, tile_matrix_set, purchase_date, min_zoom, max_zoom FROM deduplicated_eagleview
        UNION ALL
        SELECT id, bounds, accesscode, storage_url, imagery_date, provider, layer_id, tile_matrix_set, purchase_date, min_zoom, max_zoom FROM cached_tiles
      ) combined
      ORDER BY purchase_date DESC
    `, [bounds.geometry, bounds.geometry, bounds.geometry, bounds.geometry])
        .then(function (data) {
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
  })
}

const urlCached = (url) => {
  console.log(url)
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT id, ST_AsGeoJSON(bounds) as bounds, accesscode as tiles_accesscode, storage_url as tiles_url, imagery_date AS date, provider AS tiles_provider FROM imagery_purchases WHERE storage_url like $1",['%' + url + '%'])
    .then(function (data) {
      resolve(data)
    })
    .catch(function (error) {
      reject(error)
    });
  })
}


const cachedStatus = (bounds, dt) => {
  return new Promise(function (resolve, reject) {

    const date = dt || 0;

    if (date != 0) {
      pdb.any("SELECT ST_AsGeoJSON(bounds) as st_asgeojson, accesscode as tiles_accesscode, storage_url as tiles_url, imagery_date as tiles_imagery_date, provider as tiles_provider, cost as tiles_cost FROM imagery_purchases WHERE ST_Contains(bounds, ST_Dilate(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326),0.90)) AND imagery_date=$2", [bounds.geometry,date])
        .then(function (data) {
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
    } else {
      pdb.any("SELECT ST_AsGeoJSON(bounds) as st_asgeojson, accesscode as tiles_accesscode, storage_url as tiles_url, imagery_date as tiles_imagery_date, provider as tiles_provider, cost as tiles_cost FROM imagery_purchases WHERE ST_Contains(bounds, ST_Dilate(ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($1)), 4326),0.90))", [bounds.geometry])
        .then(function (data) {
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
    }
  })

}

const updatePath = (id, path) => {
  return new Promise(function (resolve, reject) {
    pdb.one('UPDATE tiles SET tiles_url=$1 WHERE tiles_id=$2 RETURNING tiles_id', [path, id])
      .then(data => {
        resolve()
      })
      .catch(error => {
        console.log(error)
        reject()
      });
  })
}

const addTile = (accesscode, path, dt, bounds, poly, provider, cost) =>
{
    return new Promise(function (resolve, reject) {
      pdb.one('INSERT INTO imagery_purchases(accesscode, storage_url, imagery_date, bounds, provider, cost) VALUES($1,$2,$3,ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($4)), 4326), $5, $6) RETURNING id', [accesscode,path,dt,poly,provider,cost])
        .then(data => {
          resolve({
            error: false,
            id: data.id
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: error
          })
        });
    })
}

const addTile2 = (accesscode, path, dt, bounds, poly, provider, cost) =>
{
    return new Promise(function (resolve, reject) {
      pdb.one('INSERT INTO imagery_purchases(accesscode, storage_url, imagery_date, bounds, provider, cost, status) VALUES($1,$2,$3,ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON($4)), 4326), $5, $6, $7) RETURNING id', [accesscode,path,dt,poly,provider,cost,'imported'])
        .then(data => {
          resolve({
            error: false,
            id: data.id
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: error
          })
        });
    })
}





const logAccess =  (tileId, isCached, selfCached,  bid, accesscode, cost, mid) => {
  var accessType = isCached == true ? selfCached == true ? 'self_cached' : 'shared_cached' : 'new_purchase'
  return new Promise(function (resolve, reject) {
    pdb.none("INSERT INTO usage_logs (purchase_id, access_type, accesscode, building_id, cost_charged, map_id, provider) VALUES($1,$2,$3,$4,$5,$6,'unknown')", [tileId, accessType, accesscode, bid, cost,mid])
        .then(() => {
          resolve({
            error: false
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: true
          })
        });
  })
}

const logAccessClick =  (tileId, bid, accesscode, cost, mid) => {
  var accessType = 'click_access'
  return new Promise(function (resolve, reject) {
    pdb.none("INSERT INTO usage_logs (purchase_id, access_type, accesscode, building_id, cost_charged, map_id, provider) VALUES($1,$2,$3,$4,$5,$6,'unknown')", [tileId, accessType, accesscode, bid, cost,mid])
        .then(() => {
          resolve({
            error: false
          })
        })
        .catch(error => {
          console.log(error)
          reject({
            error: true
          })
        });
  })
}

const addTileAccess = (tileID, accessCode) => {
  return new Promise(function (resolve, reject) {
    pdb.one('UPDATE imagery_purchases SET accesscode=$1 WHERE id=$2 RETURNING id', [accessCode, tileID])
      .then(data => {
        resolve(data)
      })
      .catch(error => {
        console.log(error)
        reject()
      });
  })

}
const getTile = (tileID) => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT ST_AsGeoJSON(bounds) as t_bounds, accesscode as tiles_accesscode, storage_url as tiles_url, imagery_date as tiles_imagery_date, provider as tiles_provider, cost as tiles_cost from imagery_purchases where id=$1", [tileID])
        .then(function (data) {
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
  })
}

const getAllTiles = () => {
  return new Promise(function (resolve, reject) {
    pdb.any("SELECT * from imagery_purchases")
        .then(function (data) {
          resolve(data)
        })
        .catch(function (error) {
          reject(error)
        });
  })
}
const getQuota = (accessCode) => {
  return new Promise(function (resolve, reject) {
    const pr1 = pdb.any("SELECT SUM(users_searches) as allocation FROM users WHERE users_accesscode = $1::text", [accessCode])
    const pr2 = pdb.any("SELECT SUM(cost_charged) as usage FROM usage_logs WHERE accesscode = $1::text AND access_type IN ('new_purchase', 'shared_cached')", [accessCode])
    Promise.all([pr1, pr2]).then(values => {
        resolve(values)
      })
      .catch(function (error) {
        reject(error)
      });
  })
}


const addQuota = (accessCode, password, quota, force, ip) => {
  return new Promise(function (resolve, reject) {
    var forcedownload = force || 0
    var quotamount = quota || 0
    if (password == 'Muj56f44f9') {
      pdb.none('INSERT INTO users (users_accesscode, users_searches, users_ip, users_forcedownload) VALUES ($1,$2,$3,$4)', [accessCode, quotamount, ip, forcedownload])
        .then(() => {
          resolve({
            error: false
          })
        })
        .catch(error => {
          reject({
            error: true
          })
        });
    } else
      reject({
        error: true
      })
  })
}
const addbypass = async (accessCode,password) => {
  try {
      // Check if accessCode exists
      if(password != 'Muj56f44f9')
        return {
          status: 'Error in addbypass function: Wrong password'
        }

      // Use new unified access_bypasses table
      const exists = await pdb.oneOrNone('SELECT id FROM access_bypasses WHERE accesscode = $1', [accessCode]);

      if (exists) {
          // If accessCode exists, delete it
          await pdb.none('DELETE FROM access_bypasses WHERE accesscode = $1', [accessCode]);
          console.log(`Removed accessCode: ${accessCode}`);
          return {
              status: `Removed accessCode: ${accessCode}`
          }
      } else {
          // If accessCode doesn't exist, insert it
          await pdb.none('INSERT INTO access_bypasses (accesscode, bypass_type) VALUES ($1, $2)', [accessCode, 'worker']);
          console.log(`Added accessCode: ${accessCode}`);
          return {
              status: `Added accessCode: ${accessCode}`
          }
      }
  } catch (error) {
      console.error('Error in addbypass function:', error);
      return {
          status: 'Error in addbypass function'
      }
  }
};

const logNearMap = async (zoom, date, jobID, accessCode, worker, bid, cost, mid, uncompressedSize, url, area) => {
  try {
      // Convert date from YYYYMMDD format to YYYY-MM-DD format
      const formattedDate = `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`;

      // Prepare the SQL statement for inserting data
      const insertSQL = `
          INSERT INTO nearmap_image_download_log 
          (zoom, date, jobID, accessCode, worker, bid, cost, mid, uncompressed_size, download_url, area)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `;

      // Execute the SQL statement with values
      await pdb.none(insertSQL, [zoom, formattedDate, jobID, accessCode, worker, bid, cost, mid, uncompressedSize, url, area]);
      
      console.log('Log entry successfully added.');
  } catch (error) {
      console.error('Error logging NearMap data:', error);
  }
};

const checkBypass = async (accessCode) => {
  try {
      // Use new unified access_bypasses table
      const exists = await pdb.oneOrNone('SELECT id FROM access_bypasses WHERE accesscode = $1 AND (expires_date IS NULL OR expires_date > NOW())', [accessCode]);
      if(exists)
        return true
      else
        return false
  } catch (error) {
      console.error('Error in checkBypass function:', error);
      return false
  }
};




const logEagleViewAccess = (accesscode, layer, zoom, x, y) => {
   return new Promise(function (resolve, reject) {
       // Use new unified usage_logs table
       pdb.none('INSERT INTO usage_logs(accesscode, provider, access_type, tile_coordinates) VALUES($1, $2, $3, $4)',
           [accesscode, 'eagleview', 'tile_access', JSON.stringify({zoom, x, y, layer})])
           .then(() => {
               resolve({ error: false });
           })
           .catch(error => {
               console.log(error);
               reject({ error: true });
           });
   });
};

const checkEagleViewPurchase = (accesscode, layer) => {
   return new Promise(function (resolve, reject) {
       // Use new unified imagery_purchases table
       pdb.any('SELECT * FROM imagery_purchases WHERE accesscode = $1 AND layer_id = $2 AND provider = $3 AND status = $4',
           [accesscode, layer, 'eagleview', 'active'])
           .then(function (data) {
               resolve(data.length > 0);
           })
           .catch(function (error) {
               reject(error);
           });
   });
};

const logEagleViewPurchase = (accesscode, layer, bounds, tileMatrixSet) => {
    const geojson = {
        type: 'Polygon',
        coordinates: [bounds],
    };

    return new Promise(function (resolve, reject) {
        // Use new unified imagery_purchases table
        pdb.one('INSERT INTO imagery_purchases(accesscode, provider, layer_id, bounds, tile_matrix_set, cost, status) VALUES($1, $2, $3, ST_SetSRID(ST_GeomFromGeoJSON($4), 4326), $5, $6, $7) RETURNING id',
            [accesscode, 'eagleview', layer, geojson, tileMatrixSet, 0, 'active'])
            .then(data => {
                resolve({
                    error: false,
                    id: data.id
                });
            })
            .catch(error => {
                console.log(error);
                reject({
                    error: true
                });
            });

    });
};

const getEagleViewPurchases = (accesscode, layer) => {
    return new Promise(function (resolve, reject) {
        // Use new unified imagery_purchases table
        pdb.any('SELECT ST_AsGeoJSON(bounds) as bounds, tile_matrix_set FROM imagery_purchases WHERE accesscode = $1 AND layer_id = $2 AND provider = $3 AND status = $4 ORDER BY purchase_date DESC',
            [accesscode, layer, 'eagleview', 'active'])
            .then(function (data) {
                if (data.length === 0) {
                    resolve({ bounds: [], tileMatrixSet: null });
                    return;
                }
                const bounds = data.map(p => JSON.parse(p.bounds).coordinates[0]);
                const tileMatrixSet = data[0].tile_matrix_set;
                resolve({ bounds, tileMatrixSet });
            })
            .catch(function (error) {
                reject(error);
            });
    });
};

// NEW: Get specific EagleView purchase by ID for ID-based communication
const getEagleViewPurchaseById = (accesscode, purchaseId) => {
    return new Promise(function (resolve, reject) {
        pdb.oneOrNone('SELECT id, layer_id, ST_AsGeoJSON(bounds) as bounds, tile_matrix_set, min_zoom, max_zoom FROM imagery_purchases WHERE accesscode = $1 AND id = $2 AND provider = $3 AND status = $4',
            [accesscode, purchaseId, 'eagleview', 'active'])
            .then(function (data) {
                if (!data) {
                    resolve(null);
                    return;
                }
                resolve({
                    id: data.id,
                    layer_id: data.layer_id,
                    bounds: JSON.parse(data.bounds).coordinates[0],
                    tile_matrix_set: data.tile_matrix_set,
                    min_zoom: data.min_zoom,
                    max_zoom: data.max_zoom
                });
            })
            .catch(function (error) {
                reject(error);
            });
    });
};

// NEW: Get any EagleView purchase by ID (for copying purposes)
const getEagleViewPurchaseByIdAny = (purchaseId) => {
    return new Promise(function (resolve, reject) {
        pdb.oneOrNone('SELECT id, layer_id, ST_AsGeoJSON(bounds) as bounds, tile_matrix_set, min_zoom, max_zoom, accesscode FROM imagery_purchases WHERE id = $1 AND provider = $2 AND status = $3',
            [purchaseId, 'eagleview', 'active'])
            .then(function (data) {
                if (!data) {
                    resolve(null);
                    return;
                }
                resolve({
                    id: data.id,
                    layer_id: data.layer_id,
                    bounds: JSON.parse(data.bounds).coordinates[0],
                    tile_matrix_set: data.tile_matrix_set,
                    min_zoom: data.min_zoom,
                    max_zoom: data.max_zoom,
                    accesscode: data.accesscode
                });
            })
            .catch(function (error) {
                reject(error);
            });
    });
};

// Unified Cost Management Functions for /api3

/**
 * Check quota and process purchase using unified cost manager
 * @param {string} accessCode - User access code
 * @param {Object} bounds - GeoJSON polygon bounds
 * @param {string} provider - Provider name
 * @param {Object} options - Additional options
 * @returns {Object} Purchase result
 */
const checkQuotaAndPurchase = async (accessCode, bounds, provider, options = {}) => {
  try {
    return await costManager.processPurchase(accessCode, bounds, provider, options);
  } catch (error) {
    console.error('Error in checkQuotaAndPurchase:', error);
    throw error;
  }
};

/**
 * Get unified quota information using cost manager
 * @param {string} accessCode - User access code
 * @returns {Object} Quota information
 */
const getUnifiedQuota = async (accessCode) => {
  try {
    return await costManager.getQuota(accessCode);
  } catch (error) {
    console.error('Error in getUnifiedQuota:', error);
    throw error;
  }
};

/**
 * Calculate cost without processing purchase
 * @param {string} accessCode - User access code
 * @param {Object} bounds - GeoJSON polygon bounds
 * @param {string} provider - Provider name
 * @param {Object} options - Additional options
 * @returns {Object} Cost calculation result
 */
const calculateCost = async (accessCode, bounds, provider, options = {}) => {
  try {
    return await costManager.calculateCost(accessCode, bounds, provider, options);
  } catch (error) {
    console.error('Error in calculateCost:', error);
    throw error;
  }
};

/**
 * Check if user has bypass privileges
 * @param {string} accessCode - User access code
 * @returns {boolean} True if user has bypass
 */
const checkUnifiedBypass = async (accessCode) => {
  try {
    return await costManager.checkBypass(accessCode);
  } catch (error) {
    console.error('Error in checkUnifiedBypass:', error);
    return false;
  }
};

module.exports = {
  // Legacy functions (for backward compatibility)
  logEagleViewAccess,
  checkEagleViewPurchase,
  logNearMap,
  isCustomer,
  logEagleViewPurchase,
  getQuota,
  addQuota,
  userStatus,
  cachedStatus,
  checkCachePic,
  checkCacheNear,
  getEagleViewPurchases,
  getEagleViewPurchaseById,
  getEagleViewPurchaseByIdAny,
  addTile,
  allCached,
  logAccess,
  updatePath,
  getAllTiles,
  addTileAccess,
  getTile,
  logAccessClick,
  copyTile,
  urlCached,
  addTile2,
  addbypass,
  checkBypass,

  // New unified cost management functions
  checkQuotaAndPurchase,
  getUnifiedQuota,
  calculateCost,
  checkUnifiedBypass,
  costManager
}