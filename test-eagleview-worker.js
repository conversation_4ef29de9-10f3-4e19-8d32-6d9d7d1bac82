// Test script to verify EagleView worker logic
const fetch = require('node-fetch');

async function testEagleViewWorkerLogic() {
    console.log('Testing EagleView worker logic...');
    
    const testAccessCode = 'test-regular-user-12345'; // Test access code without bypass
    const testBounds = [[-77.539, 40.005], [-77.524, 40.005], [-77.524, 40.013], [-77.539, 40.013], [-77.539, 40.005]];
    
    // Test 1: Regular user purchase (should be charged)
    console.log('\n=== Test 1: Regular User Purchase ===');
    const regularUserData = {
        accesscode: testAccessCode,
        layer: 'test-layer-2023-08-15',
        bounds: testBounds,
        tileMatrixSet: 'GoogleMapsCompatible_9-23',
        imageryDate: '2023-08-15',
        worker: 0 // Regular user
    };
    
    try {
        const response1 = await fetch('http://localhost:3002/api3/tiles/eagleview/purchase', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(regularUserData)
        });
        
        console.log('Regular user response status:', response1.status);
        const responseText1 = await response1.text();
        console.log('Regular user response:', responseText1);
        
        if (response1.status === 200) {
            const result1 = JSON.parse(responseText1);
            console.log('Regular user cost:', result1.cost);
            console.log('Regular user reason:', result1.reason);
        }
        
    } catch (error) {
        console.error('Regular user request error:', error.message);
    }
    
    // Test 2: Worker purchase (should be free)
    console.log('\n=== Test 2: Worker Purchase ===');
    const workerData = {
        accesscode: testAccessCode,
        layer: 'test-layer-2023-08-15-worker',
        bounds: testBounds,
        tileMatrixSet: 'GoogleMapsCompatible_9-23',
        imageryDate: '2023-08-15',
        worker: 1 // Worker
    };
    
    try {
        const response2 = await fetch('http://localhost:3002/api3/tiles/eagleview/purchase', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(workerData)
        });
        
        console.log('Worker response status:', response2.status);
        const responseText2 = await response2.text();
        console.log('Worker response:', responseText2);
        
        if (response2.status === 200) {
            const result2 = JSON.parse(responseText2);
            console.log('Worker cost:', result2.cost);
            console.log('Worker reason:', result2.reason);
            
            // Verify worker gets free access
            if (result2.cost === 0 && result2.reason === 'worker_bypass') {
                console.log('✅ Worker logic working correctly - free access granted');
            } else {
                console.log('❌ Worker logic not working - worker should get free access');
            }
        }
        
    } catch (error) {
        console.error('Worker request error:', error.message);
    }
    
    // Test 3: Test cached search with worker parameter
    console.log('\n=== Test 3: Cached Search with Worker Parameter ===');
    const boundsString = '-77.539,40.005,-77.524,40.013';
    
    try {
        // Test regular user cached search
        const cachedUrl1 = `http://localhost:3002/api3/tiles/searchcached?accesscode=${testAccessCode}&bounds=${boundsString}&worker=0`;
        const cachedResponse1 = await fetch(cachedUrl1);
        const cachedData1 = await cachedResponse1.json();
        console.log('Regular user cached results count:', cachedData1.length);

        // Test worker cached search
        const cachedUrl2 = `http://localhost:3002/api3/tiles/searchcached?accesscode=${testAccessCode}&bounds=${boundsString}&worker=1`;
        const cachedResponse2 = await fetch(cachedUrl2);
        const cachedData2 = await cachedResponse2.json();
        console.log('Worker cached results count:', cachedData2.length);
        
        // Workers should see at least as many results as regular users (potentially more EagleView results)
        if (cachedData2.length >= cachedData1.length) {
            console.log('✅ Worker cached search working correctly');
        } else {
            console.log('❌ Worker cached search may have issues');
        }
        
    } catch (error) {
        console.error('Cached search test error:', error.message);
    }
    
    console.log('\n=== Test Complete ===');
}

// Run the test
testEagleViewWorkerLogic();
