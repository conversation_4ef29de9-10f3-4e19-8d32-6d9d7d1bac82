// Unified Cost Manager for /api3
// Based on cost-quota-consolidation-strategy.md
// Handles cost calculation and quota enforcement across all providers

const turf = require('@turf/turf');
const pgp = require('pg-promise')();

class CostManager {
    constructor(database) {
        this.db = database;
        this.PRICING_FACTOR = 15000;
        this.SQM_TO_ACRES = 0.000247105;
        this.MIN_COST = 1; // Minimum $1 charge
    }

    /**
     * Calculate cost for a given bounds and check quota availability
     * @param {string} accessCode - User access code
     * @param {Object} bounds - GeoJSON polygon bounds
     * @param {string} provider - Provider name ('nearmap', 'eagleview', 'pictometry')
     * @param {Object} options - Additional options (isWorker, layerId, etc.)
     * @returns {Object} Cost calculation result
     */
    async calculateCost(accessCode, bounds, provider, options = {}) {
        try {
            console.log(`CostManager: Calculating cost for ${provider} - ${accessCode}`);

            // 1. Check worker bypass first
            if (options.isWorker || await this.checkBypass(accessCode)) {
                console.log(`CostManager: Worker/bypass access granted for ${accessCode}`);

                // Still get quota info for display purposes
                const quota = await this.getQuota(accessCode);

                return {
                    cost: 0,
                    reason: 'worker_bypass',
                    canProceed: true,
                    remainingQuota: quota.remaining,
                    allocation: quota.allocation,
                    usage: quota.usage,
                    bypassType: options.isWorker ? 'worker' : 'admin'
                };
            }

            // 2. Check previous purchases (free access rule)
            const previousPurchase = await this.findOverlappingPurchase(accessCode, bounds, provider, options);
            if (previousPurchase) {
                console.log(`CostManager: Previous purchase found for ${accessCode}, granting free access`);

                // Still get quota info for display purposes
                const quota = await this.getQuota(accessCode);

                return {
                    cost: 0,
                    reason: 'previous_purchase',
                    purchaseId: previousPurchase.id,
                    canProceed: true,
                    remainingQuota: quota.remaining,
                    allocation: quota.allocation,
                    usage: quota.usage
                };
            }

            // 3. Calculate area-based cost
            const areaCost = this.calculateAreaCost(bounds);
            console.log(`CostManager: Calculated area cost: $${areaCost}`);

            // 4. Check quota availability
            const quota = await this.getQuota(accessCode);
            const canProceed = quota.remaining >= areaCost;

            console.log(`CostManager: Quota check - Required: $${areaCost}, Available: $${quota.remaining}, Can proceed: ${canProceed}`);

            return {
                cost: areaCost,
                reason: 'new_purchase',
                canProceed: canProceed,
                remainingQuota: quota.remaining,
                requiredQuota: areaCost,
                allocation: quota.allocation,
                usage: quota.usage
            };

        } catch (error) {
            console.error('CostManager: Error calculating cost:', error);
            throw error;
        }
    }

    /**
     * Calculate cost based on area using the standard pricing formula
     * @param {Object} bounds - GeoJSON polygon bounds
     * @returns {number} Cost in dollars
     */
    calculateAreaCost(bounds) {
        const boundsArea = turf.area(bounds);
        const areaInAcres = boundsArea * this.SQM_TO_ACRES;
        let dollarEstimate = Math.ceil((areaInAcres * 0.25) * 2) / 2;
        return dollarEstimate < this.MIN_COST ? this.MIN_COST : dollarEstimate;
    }

    /**
     * Process a purchase: check quota, record purchase, log usage
     * @param {string} accessCode - User access code
     * @param {Object} bounds - GeoJSON polygon bounds
     * @param {string} provider - Provider name
     * @param {Object} options - Additional options
     * @returns {Object} Purchase result
     */
    async processPurchase(accessCode, bounds, provider, options = {}) {
        try {
            console.log(`CostManager: Processing purchase for ${provider} - ${accessCode}`);

            const costResult = await this.calculateCost(accessCode, bounds, provider, options);

            if (!costResult.canProceed) {
                console.log(`CostManager: Purchase denied - insufficient quota`);
                return {
                    success: false,
                    error: 'insufficient_quota',
                    required: costResult.requiredQuota,
                    available: costResult.remainingQuota
                };
            }

            // Record the purchase
            const purchaseId = await this.recordPurchase(accessCode, bounds, provider, costResult.cost, options);
            
            // Log the usage
            await this.logUsage(accessCode, provider, purchaseId, costResult.cost, costResult.reason, options);

            console.log(`CostManager: Purchase successful - ID: ${purchaseId}, Cost: $${costResult.cost}`);

            return {
                success: true,
                purchaseId: purchaseId,
                cost: costResult.cost,
                reason: costResult.reason,
                remainingQuota: costResult.remainingQuota - costResult.cost
            };

        } catch (error) {
            console.error('CostManager: Error processing purchase:', error);
            throw error;
        }
    }

    /**
     * Check if user has bypass privileges
     * @param {string} accessCode - User access code
     * @returns {boolean} True if user has bypass
     */
    async checkBypass(accessCode) {
        try {
            const result = await this.db.oneOrNone(
                'SELECT bypass_type FROM access_bypasses WHERE accesscode = $1 AND (expires_date IS NULL OR expires_date > NOW())',
                [accessCode]
            );
            return result !== null;
        } catch (error) {
            console.error('CostManager: Error checking bypass:', error);
            return false;
        }
    }

    /**
     * Find overlapping previous purchases for free access
     * @param {string} accessCode - User access code
     * @param {Object} bounds - GeoJSON polygon bounds
     * @param {string} provider - Provider name
     * @param {Object} options - Additional options
     * @returns {Object|null} Previous purchase if found
     */
    async findOverlappingPurchase(accessCode, bounds, provider, options = {}) {
        try {
            let query = `
                SELECT id, bounds, layer_id, tile_matrix_set 
                FROM imagery_purchases 
                WHERE accesscode = $1 
                AND provider = $2 
                AND status = 'active'
                AND ST_Contains(bounds, ST_SetSRID(ST_GeomFromGeoJSON($3), 4326))
            `;
            
            const params = [accessCode, provider, JSON.stringify(bounds)];

            // For EagleView, also match layer_id
            if (provider === 'eagleview' && options.layerId) {
                query += ' AND layer_id = $4';
                params.push(options.layerId);
            }

            query += ' LIMIT 1';

            const result = await this.db.oneOrNone(query, params);
            return result;
        } catch (error) {
            console.error('CostManager: Error finding overlapping purchase:', error);
            return null;
        }
    }

    /**
     * Get user quota information
     * @param {string} accessCode - User access code
     * @returns {Object} Quota information
     */
    async getQuota(accessCode) {
        try {
            const allocationResult = await this.db.oneOrNone(
                'SELECT SUM(users_searches) as allocation FROM users WHERE users_accesscode = $1',
                [accessCode]
            );

            const usageResult = await this.db.oneOrNone(
                'SELECT SUM(cost_charged) as usage FROM usage_logs WHERE accesscode = $1 AND access_type IN ($2, $3)',
                [accessCode, 'new_purchase', 'shared_cached']
            );

            const allocation = parseFloat(allocationResult?.allocation || 0);
            const usage = parseFloat(usageResult?.usage || 0);
            const remaining = allocation - usage;

            return {
                allocation: allocation,
                usage: usage,
                remaining: remaining
            };
        } catch (error) {
            console.error('CostManager: Error getting quota:', error);
            throw error;
        }
    }

    /**
     * Record a purchase in the imagery_purchases table
     * @param {string} accessCode - User access code
     * @param {Object} bounds - GeoJSON polygon bounds
     * @param {string} provider - Provider name
     * @param {number} cost - Cost amount
     * @param {Object} options - Additional options
     * @returns {number} Purchase ID
     */
    async recordPurchase(accessCode, bounds, provider, cost, options = {}) {
        try {
            const result = await this.db.one(`
                INSERT INTO imagery_purchases (
                    accesscode, provider, layer_id, bounds, tile_matrix_set,
                    imagery_date, cost, storage_url, status, min_zoom, max_zoom
                ) VALUES ($1, $2, $3, ST_SetSRID(ST_GeomFromGeoJSON($4), 4326), $5, $6, $7, $8, $9, $10, $11)
                RETURNING id
            `, [
                accessCode,
                provider,
                options.layerId || null,
                JSON.stringify(bounds),
                options.tileMatrixSet || null,
                options.imageryDate || null,
                cost,
                options.storageUrl || null,
                'active',
                options.minZoom || 7,
                options.maxZoom || 22
            ]);

            return result.id;
        } catch (error) {
            console.error('CostManager: Error recording purchase:', error);
            throw error;
        }
    }

    /**
     * Log usage in the usage_logs table
     * @param {string} accessCode - User access code
     * @param {string} provider - Provider name
     * @param {number} purchaseId - Purchase ID
     * @param {number} cost - Cost charged
     * @param {string} accessType - Type of access
     * @param {Object} options - Additional options
     */
    async logUsage(accessCode, provider, purchaseId, cost, accessType, options = {}) {
        try {
            await this.db.none(`
                INSERT INTO usage_logs (
                    accesscode, provider, purchase_id, access_type, cost_charged,
                    building_id, map_id, tile_coordinates, uncompressed_size, area_acres
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            `, [
                accessCode,
                provider,
                purchaseId,
                accessType,
                cost,
                options.buildingId || null,
                options.mapId || null,
                options.tileCoordinates ? JSON.stringify(options.tileCoordinates) : null,
                options.uncompressedSize || null,
                options.areaAcres || null
            ]);
        } catch (error) {
            console.error('CostManager: Error logging usage:', error);
            throw error;
        }
    }
}

module.exports = CostManager;
